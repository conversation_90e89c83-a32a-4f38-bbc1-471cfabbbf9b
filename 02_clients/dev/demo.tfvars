parent_folder = "folders/330809538592"
client_prefix   = "demo"
app_owner       = "demo-owner"
environment   = "dev"
psa_cidr      = "10.30.8.0/21"
subnet_config = {
  "subnet1" = {
    subnet_purpose     = "egr"
    primary_cidr_range = "*********/22"
  }
}
dns_zone_type = ["private"]

# Example Cloud SQL instances configuration
# Uncomment and modify as needed for your specific requirements
cloud_sql_instances = {
  "demo-app-db" = {
    name                = "demo-app-postgres"
    engine              = "POSTGRES_15"
    machine_type        = "db-f1-micro"
    disk_size           = 20
    region              = "europe-west2"
    availability_type   = "ZONAL"
    backup_enabled      = true
    backup_region       = "europe-west2"
    backup_start_time   = "04:00"
    enable_public_ip    = false
    custom_labels = {
      environment = "dev"
      application = "demo-app"
    }
    deletion_protection = true
  }
  
  "demo-analytics-db" = {
    name                = "demo-analytics-mysql"
    engine              = "MYSQL_8_0"
    machine_type        = "db-g1-small"
    disk_size           = 50
    region              = "europe-west2"
    availability_type   = "ZONAL"
    backup_enabled      = true
    backup_region       = "europe-west2"
    backup_start_time   = "02:00"
    enable_public_ip    = false
    custom_labels = {
      environment = "dev"
      application = "analytics"
    }
    deletion_protection = true
  }
}

# Example Redis instances configuration
# Uncomment and modify as needed for your specific requirements
redis_instances = {
  "demo-cache" = {
    name           = "demo-redis-cache"
    memory_size_gb = 1
    region         = "europe-west2"
    display_name   = "Demo Redis Cache"
    custom_labels = {
      environment = "dev"
      application = "demo-cache"
    }
  }
  
  "demo-session" = {
    name           = "demo-redis-session"
    memory_size_gb = 2
    region         = "europe-west2"
    display_name   = "Demo Session Store"
    custom_labels = {
      environment = "dev"
      application = "demo-session"
    }
  }
}

rules = [
  {
    name               = "allow-all-pvt"
    direction          = "INGRESS"
    source_ranges      = ["*********/20"]
    destination_ranges = ["*********/20"]
    priority           = 55000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-all-pvt"
    direction          = "EGRESS"
    source_ranges      = ["*********/20"]
    destination_ranges = ["*********/20"]
    priority           = 58000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-http-egress"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 59000
    allow = [{
      protocol = "tcp"
      ports    = ["80", "443"]
    }]
    deny = []
  },
  {
    name               = "allow-sql-access"
    direction          = "INGRESS"
    source_ranges      = ["*********/20"]
    destination_ranges = ["10.30.8.0/21"]
    priority           = 56000
    allow = [{
      protocol = "tcp"
      ports    = ["5432", "3306"]
    }]
    deny = []
  },
  {
    name               = "deny-all"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 60000
    allow              = []
    deny = [{
      protocol = "all"
      ports    = []
    }]
  }
]


