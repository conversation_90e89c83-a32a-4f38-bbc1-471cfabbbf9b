client_prefix   = "demo"
app_owner       = "demo-owner"
environment   = "qa"
parent_folder = "folders/419368429032"
psa_cidr      = "10.30.24.0/21"
subnet_config = {
  "subnet1" = {
    subnet_purpose     = "egr"
    primary_cidr_range = "**********/22"
  }
}
dns_zone_type = ["private"]

# Example Cloud SQL instances configuration for QA environment
cloud_sql_instances = {
  "qa-app-db" = {
    name                = "qa-app-postgres"
    engine              = "POSTGRES_15"
    machine_type        = "db-g1-small"
    disk_size           = 30
    region              = "europe-west2"
    availability_type   = "ZONAL"
    backup_enabled      = true
    backup_region       = "europe-west2"
    backup_start_time   = "03:00"
    enable_public_ip    = false
    custom_labels = {
      environment = "qa"
      application = "app"
    }
    deletion_protection = true
  }
  
  "qa-reporting-db" = {
    name                = "qa-reporting-postgres"
    engine              = "POSTGRES_15"
    machine_type        = "db-n1-standard-1"
    disk_size           = 100
    region              = "europe-west2"
    availability_type   = "ZONAL"
    backup_enabled      = true
    backup_region       = "europe-west2"
    backup_start_time   = "01:00"
    enable_public_ip    = false
    custom_labels = {
      environment = "qa"
      application = "reporting"
    }
    deletion_protection = true
  }
}

rules = [
  {
    name               = "allow-all-pvt"
    direction          = "INGRESS"
    source_ranges      = ["**********/20"]
    destination_ranges = ["**********/20"]
    priority           = 55000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-all-pvt"
    direction          = "EGRESS"
    source_ranges      = ["**********/20"]
    destination_ranges = ["**********/20"]
    priority           = 58000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-http-egress"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 59000
    allow = [{
      protocol = "tcp"
      ports    = ["80", "443"]
    }]
    deny = []
  },
  {
    name               = "allow-sql-access"
    direction          = "INGRESS"
    source_ranges      = ["**********/20"]
    destination_ranges = ["10.30.24.0/21"]
    priority           = 56000
    allow = [{
      protocol = "tcp"
      ports    = ["5432", "3306"]
    }]
    deny = []
  },
  {
    name               = "deny-all"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 60000
    allow              = []
    deny = [{
      protocol = "all"
      ports    = []
    }]
  }
]


