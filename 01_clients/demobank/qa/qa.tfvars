environment   = "qa"
parent_folder = "folders/419368429032"
psa_cidr      = "10.30.24.0/21"
subnet_config = {
  "subnet1" = {
    subnet_purpose     = "egr"
    primary_cidr_range = "10.30.16.0/22"
  }
}
dns_zone_type = ["private"]
rules = [
  {
    name               = "allow-all-pvt"
    direction          = "INGRESS"
    source_ranges      = ["10.30.16.0/20"]
    destination_ranges = ["10.30.16.0/20"]
    priority           = 55000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-all-pvt"
    direction          = "EGRESS"
    source_ranges      = ["10.30.16.0/20"]
    destination_ranges = ["10.30.16.0/20"]
    priority           = 58000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-http-egress"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 59000
    allow = [{
      protocol = "tcp"
      ports    = ["80", "443"]
    }]
    deny = []
  },
  {
    name               = "deny-all"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 60000
    allow              = []
    deny = [{
      protocol = "all"
      ports    = []
    }]
  }
]

gcs_bucket_config = {
   "bucket1" = {
     bucket_name= "function-bankinterface"
  },
   "bucket2"= {
    bucket_name = "portal-frontend"
  }
}

################ Service account config ##############


service_accounts_config = {
  "cloudrun" = {
    service_account_purpose = "cloud-run"
  }
}


########### pub sub #############

pubsub_config = {
#------- functioncoreapi-process-confirmed-transactions ---------#
  "LambdaCoreApi-process-confirmed-transactions-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "LambdaCoreApi-process-confirmed-transactions-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "LambdaCoreApi-process-confirmed-transactions-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "LambdaCoreApi-process-confirmed-transactions-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "LambdaCoreApi-process-confirmed-transactions-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },
  #------- bank-interface-process-authorisation-topic ---------#
  "bank-interface-process-authorisation-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-authorisation-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-authorisation-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-authorisation-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-authorisation-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },
#------- bank-interface-process-clear-topic ---------#
"bank-interface-process-clear-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-clear-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-clear-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-clear-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-clear-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },
#------- bank-interface-process-credit-topic ---------#
"bank-interface-process-credit-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-credit-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-credit-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-credit-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-credit-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-fee ---------#
"bank-interface-process-fee-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-fee-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-fee-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-fee-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-fee-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-incoming ---------#
"bank-interface-process-incoming-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-incoming-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-incoming-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-incoming-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-incoming-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-incoming-institutional ---------#
"bank-interface-process-incoming-institutional-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-incoming-institutional-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-incoming-institutional-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-incoming-institutional-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-incoming-institutional-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  }, 

#------- bank-interface-process-incoming-monitoring ---------#
"bank-interface-process-incoming-monitoring-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-incoming-monitoring-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-incoming-monitoring-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-incoming-monitoring-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-incoming-monitoring-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-incoming-monitoring-result ---------#
"bank-interface-process-incoming-monitoring-result-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-incoming-monitoring-result-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-incoming-monitoring-result-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-incoming-monitoring-result-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-incoming-monitoring-result-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-outgoing ---------#
"bank-interface-process-outgoing-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
    "bank-interface-process-outgoing-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-outgoing-authorisation ---------#
"bank-interface-process-outgoing-authorisation-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-authorisation-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-authorisation-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-authorisation-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-authorisation-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

#------- bank-interface-process-outgoing-institutional ---------#
"bank-interface-process-outgoing-institutional-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-institutional-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-institutional-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-institutional-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-institutional-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },
 
 #------- bank-interface-process-outgoing-monitoring ---------#
"bank-interface-process-outgoing-monitoring-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-monitoring-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-monitoring-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-monitoring-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-monitoring-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

   #------- bank-interface-process-outgoing-monitoring-result ---------#
"bank-interface-process-outgoing-monitoring-result-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-monitoring-result-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-monitoring-result-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-monitoring-result-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-monitoring-result-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- bank-interface-process-outgoing-return ---------#
"bank-interface-process-outgoing-return-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-return-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-return-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-return-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-return-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- bank-interface-process-outgoing-transfer ---------#
"bank-interface-process-outgoing-transfer-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-outgoing-transfer-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-outgoing-transfer-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-outgoing-transfer-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-outgoing-transfer-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- bank-interface-process-release ---------#
"bank-interface-process-release-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-release-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-release-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-release-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-release-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- bank-interface-process-return ---------#
"bank-interface-process-return-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "bank-interface-process-return-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "bank-interface-process-return-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "bank-interface-process-return-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "bank-interface-process-return-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- event-processor ---------#
"event-processor-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "event-processor-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "event-processor-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "event-processor-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "event-processor-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- transaction-processor ---------#
"transaction-processor-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "transaction-processor-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "transaction-processor-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "transaction-processor-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "transaction-processor-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  },

 #------- webhooks ---------#
"webhooks-topic" = {
    message_retention_duration = "345600s"
    subscriptions = {
      "webhooks-subscription" = {
        ack_deadline_seconds         = 60
        message_retention_duration   = "345600s"
        retry_policy = {
          minimum_backoff = "10s"
          maximum_backoff = "60s"
        }
      }
    }
  },
  "webhooks-dead-letter-topic" = {
    message_retention_duration = "1209600s"
    subscriptions = {
      "webhooks-dead-letter-subscription" = {
        ack_deadline_seconds         = 30
        message_retention_duration   = "1209600s"
        dead_letter_policy = {
          dead_letter_topic_key = "webhooks-topic"
          max_delivery_attempts = 10
        }
        retry_policy = {
          minimum_backoff = "5s"
          maximum_backoff = "30s"
        }
      }
    }
  }
}