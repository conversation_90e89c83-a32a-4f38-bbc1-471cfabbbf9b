environment   = "stg"
parent_folder = "folders/479187171164"
psa_cidr      = "10.30.56.0/21"
subnet_config = {
  "subnet1" = {
    subnet_purpose     = "egr"
    primary_cidr_range = "10.30.48.0/22"
  }
}
dns_zone_type = ["private"]
rules = [
  {
    name               = "allow-all-pvt"
    direction          = "INGRESS"
    source_ranges      = ["10.30.48.0/20"]
    destination_ranges = ["10.30.48.0/20"]
    priority           = 55000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-all-pvt"
    direction          = "EGRESS"
    source_ranges      = ["10.30.48.0/20"]
    destination_ranges = ["10.30.48.0/20"]
    priority           = 58000
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny = []
  },
  {
    name               = "allow-http-egress"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 59000
    allow = [{
      protocol = "tcp"
      ports    = ["80", "443"]
    }]
    deny = []
  },
  {
    name               = "deny-all"
    direction          = "EGRESS"
    source_ranges      = ["0.0.0.0/0"]
    destination_ranges = ["0.0.0.0/0"]
    priority           = 60000
    allow              = []
    deny = [{
      protocol = "all"
      ports    = []
    }]
  }
]

gcs_bucket_config = {}