# Redis instances configuration
# This file creates multiple Redis instances based on the redis_instances variable


# Create Redis instances dynamically
module "redis_instances" {
  source = "../../gcp_core_modules/redis_instance"
  
  for_each = var.redis_instances

  project               = module.project.project_id
  name                  = each.value.name
  tier                  = each.value.tier
  memory_size_gb        = each.value.memory_size_gb
  region                = each.value.region
  authorized_network    = module.networking.vpc_name
  display_name          = each.value.display_name
  redis_version         = each.value.redis_version
  reserved_ip_range     = each.value.reserved_ip_range
  alternative_location_id = each.value.alternative_location_id
  transit_encryption_mode = each.value.transit_encryption_mode
  auth_enabled          = each.value.auth_enabled
  maintenance_policy    = each.value.maintenance_policy
  labels                = merge(
    {
      "environment" = var.environment,
      "client"      = var.client_prefix,
      "managed-by"  = "terraform"
    },
    each.value.custom_labels
  )
  persistence_config  = each.value.persistence_config
}

# # Outputs for Redis instances
# output "redis_instance_names" {
#   description = "Names of all created Redis instances"
#   value       = { for k, v in module.redis_instances : k => v.name }
# }

# output "redis_instance_hosts" {
#   description = "Host addresses of all created Redis instances"
#   value       = { for k, v in module.redis_instances : k => v.host }
# }

# output "redis_instance_ports" {
#   description = "Ports of all created Redis instances"
#   value       = { for k, v in module.redis_instances : k => v.port }
# }

# output "redis_instance_connection_strings" {
#   description = "Connection strings of all created Redis instances"
#   value       = { for k, v in module.redis_instances : k => "redis://${v.host}:${v.port}" }
# }
