# Cloud SQL instances configuration
# This file creates multiple Cloud SQL instances based on the cloud_sql_instances variable

# Create Cloud SQL instances dynamically
module "cloud_sql_instances" {
  source = "../../gcp_core_modules/cloud_sql"
  
  for_each = var.cloud_sql_instances

  project_id          = module.project.project_id
  host_project_id     = module.project.project_id
  region              = each.value.region
  name                = each.value.name
  engine              = each.value.engine
  machine_type        = each.value.machine_type
  disk_size           = each.value.disk_size
  disk_type           = each.value.disk_type
  availability_type   = each.value.availability_type
  backup_enabled      = each.value.backup_enabled
  backup_region       = each.value.backup_region
  backup_start_time   = each.value.backup_start_time
  authorized_networks = each.value.authorized_networks
  enable_public_internet_access = each.value.enable_public_ip
  network_name        = module.networking.vpc_name
  custom_labels       = each.value.custom_labels
  deletion_protection = each.value.deletion_protection
  database_flags      = each.value.database_flags
}

# # Outputs for Cloud SQL instances
# output "cloud_sql_instance_names" {
#   description = "Names of all created Cloud SQL instances"
#   value       = { for k, v in module.cloud_sql_instances : k => v.instance_name }
# }

# output "cloud_sql_instance_ips" {
#   description = "Private IP addresses of all created Cloud SQL instances"
#   value       = { for k, v in module.cloud_sql_instances : k => v.private_ip_address }
# }

# output "cloud_sql_instance_connection_names" {
#   description = "Connection names of all created Cloud SQL instances"
#   value       = { for k, v in module.cloud_sql_instances : k => v.connection_name }
# }
