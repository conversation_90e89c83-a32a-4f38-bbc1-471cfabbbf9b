
###################################
########## Google Project #########
###################################

variable "org_id" {
  type        = string
  default     = ""
  description = "organization ID"
}

variable "environment" {
  type        = string
  description = "Environment name"
  default     = "dev"
}

variable "client_prefix" {
  type        = string
  description = "Client prefix"
  default     = "testbank"
}

variable "billing_account" {
  type        = string
  description = "The alphanumeric ID of the billing account this project belongs to"
  default     = "01696D-176235-14EB50"
}

variable "region_prefix" {
  type        = string
  description = "Prefix for region"
  default     = "ew2"
}

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project"
  default = [
    "compute.googleapis.com",
    "iam.googleapis.com",
    "stackdriver.googleapis.com",
    "iamcredentials.googleapis.com",
    "dns.googleapis.com",
    "servicenetworking.googleapis.com",
    "networkmanagement.googleapis.com",
    "container.googleapis.com",
    "iap.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "dataflow.googleapis.com",
    "secretmanager.googleapis.com",
    "privilegedaccessmanager.googleapis.com"
  ]
}

###################################
######## Google Networking ########
###################################


variable "region" {
  type        = string
  default     = "europe-west2"
  description = "Region for the deployment of resources"
}


###################################
######### Google Security #########
###################################

variable "rules" {
  type = list(object({
    name                    = string
    description             = optional(string)
    direction               = string
    priority                = number
    source_ranges           = list(string)
    destination_ranges      = list(string)
    source_tags             = optional(list(string), null)
    source_service_accounts = optional(list(string), null)
    target_tags             = optional(list(string), null)
    target_service_accounts = optional(list(string), null)
    allow = list(object({
      protocol = string
      ports    = list(string)
    }))
    deny = list(object({
      protocol = string
      ports    = list(string)
    }))
    log_config = optional(object({
      metadata = string
    }), null)
  }))
  description = "Firewall rules"
}

# variable "blocked_ips" {
#   description = "IP addresses or CIDR ranges to block."
#   type        = list(string)
#   default     = []
# }

variable "armor_config" {
  description = " To enable/disable armor rules and set priority"
  type = map(object({
    enabled  = bool
    priority = number
  }))
}

variable "subnet_config" {
  type = map(object({
    subnet_purpose     = string
    primary_cidr_range = string
  }))
}

variable "parent_folder" {
  type = string
}

variable "app_owner" {
  type = string
}

variable "psa_cidr" {
  type        = string
  description = "CIDR range for PSA"
}

variable "alert_config" {
  type = list(object({
    create_iam_metric           = optional(bool, false)
    create_custom_role_metric   = optional(bool, false)
    create_owner_role_metric    = optional(bool, false)
    create_bucket_iam_metric    = optional(bool, false)
    create_gce_alert            = optional(bool, false)
    create_sql_alert            = optional(bool, false)
    create_pubsub_alert         = optional(bool, false)
    create_gke_alert            = optional(bool, false)
    create_redis_alert          = optional(bool, false)
    create_cloud_run_alert      = optional(bool, false)
    create_cloud_function_alert = optional(bool, false)
    create_firewall_metric      = optional(bool, false)
    create_network_metric       = optional(bool, false)
    create_route_metric         = optional(bool, false)
  }))
  description = "Alerting configuration"
}


variable "dns_zone_type" {
  type        = list(string)
  description = "public & private DNS zone types"
}

variable "gcs_bucket_config" {
  type = map(object({
    bucket_name= string
  }))
  description = "Configuration for the bucket name."
}


variable "service_accounts_config" {
  type    = any
  
}
variable "pubsub_config" {
  type    = any

}

variable "cloud_sql_instances" {
  description = "Map of Cloud SQL instances to create"
  type = map(object({
    name                = string
    engine              = string
    machine_type        = string
    disk_size           = optional(number, 10)
    disk_type           = optional(string, "PD_SSD")
    region              = optional(string, null)
    availability_type   = optional(string, "ZONAL")
    backup_enabled      = optional(bool, false)
    backup_region       = optional(string, null)
    backup_start_time   = optional(string, "04:00")
    authorized_networks = optional(list(map(string)), [])
    enable_public_ip    = optional(bool, false)
    database_flags      = optional(list(object({
      name  = string
      value = string
    })), [])
    custom_labels = optional(map(string), {})
    deletion_protection = optional(bool, true)
  }))
  default = {}
}

variable "redis_instances" {
  description = "Map of Redis instances to create"
  type = map(object({
    name           = string
    tier           = optional(string, "BASIC")
    memory_size_gb = number
    region         = optional(string, null)
    display_name   = optional(string, null)
    redis_version  = optional(string, "REDIS_7_0")
    reserved_ip_range = optional(string, null)
    alternative_location_id = optional(string, null)
    transit_encryption_mode = optional(string, "SERVER_AUTHENTICATION")
    auth_enabled = optional(bool, true)
    maintenance_policy = optional(object({
      day = string
      start_time = string
    }), null)
    custom_labels = optional(map(string), {})
    persistence_config = optional(object({
      persistence_mode = optional(string, "DISABLED")
      rdb_snapshot_period = optional(string, null)
      rdb_snapshot_start_time = optional(string, null)
    }), null)
  }))
  default = {}
}

