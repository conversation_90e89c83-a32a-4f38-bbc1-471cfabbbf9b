name: Deploy Infrastructure with Terraform and IAC Validation

on:
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main
    paths-ignore:
      - '**/README.md'
      - '.gitignore'
      - 'gcp_core_modules/**'

env:
  
  TF_VERSION: "~> 1.8"
  TF_PLAN_FILE: "tfplan"

jobs:
  discover-changes:
    name: Discover Changed Environments
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      pull-requests: 'read'
    outputs:
      matrix_json: ${{ steps.determine-matrix.outputs.matrix_json }}
      has_changes: ${{ steps.determine-matrix.outputs.has_changes }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get changed environment-specific tfvars files
        id: changed_tfvars_files
        uses: tj-actions/changed-files@v44
        with:
          files: |
            01_clients/**/**.tfvars
            00_internal/**.tfvars
          files_ignore: |
            gcp_core_modules/**
            01_clients/*/common.tfvars

      - name: Get changed client common.tfvars files
        id: changed_client_common_tfvars
        uses: tj-actions/changed-files@v44
        with:
          files: |
            01_clients/*/common.tfvars
          files_ignore: |
            gcp_core_modules/**

      - name: Determine Terraform Matrix
        id: determine-matrix
        shell: bash
        run: |
          set -eo pipefail # Exit on error and treat pipe failures as errors

          # Get outputs from previous steps
          TFVARS_FILES="${{ steps.changed_tfvars_files.outputs.all_changed_and_modified_files }}"
          CLIENT_COMMON_TFVARS="${{ steps.changed_client_common_tfvars.outputs.all_changed_and_modified_files }}"

          # Use a bash associative array to store configs and prevent duplicates easily.
          # The key will be the environment-specific var file, ensuring each environment is listed only once.
          declare -A CONFIGS_MAP

          # Function to add a configuration to the map.
          add_config() {
            local config="$1"
            local key
            # Extract the env_var_file path to use as a unique key for the map
            key=$(echo "$config" | jq -r .env_var_file)
            if [[ -n "$key" ]]; then
              CONFIGS_MAP["$key"]="$config"
            fi
          }

          # --- RULE 1: A client's 'common.tfvars' change triggers all environments for that client ---
          if [[ -n "$CLIENT_COMMON_TFVARS" ]]; then
            echo "::notice::Client 'common.tfvars' changed. Expanding all environments for affected clients."
            for common_file in $CLIENT_COMMON_TFVARS; do
              CLIENT_DIR=$(dirname "$common_file")
              CLIENT=$(basename "$CLIENT_DIR")
              # Find all environment tfvars for this specific client
              CLIENT_ENV_TFVARS=$(find "$CLIENT_DIR" -type f -name "*.tfvars" ! -name "common.tfvars" || true)
              for env_file in $CLIENT_ENV_TFVARS; do
                ENV_DIR=$(dirname "$env_file")
                ENV=$(basename "$ENV_DIR")
                # CORRECTED: workdir is now the wrapper module directory.
                WORKDIR="wrapper_modules/01_client"
                # CORRECTED: Paths are now relative from the wrapper module directory.
                CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"$CLIENT\",\"env\":\"$ENV\",\"backend_prefix\":\"terraform/$CLIENT/$ENV\",\"common_var_file\":\"../../$common_file\",\"env_var_file\":\"../../$env_file\"}"
                add_config "$CONFIG"
              done
            done
          fi

          # --- RULE 2: A specific '.tfvars' file change triggers only itself ---
          # This runs last and checks if the config already exists from a broader change to avoid redundant runs.
          if [[ -n "$TFVARS_FILES" ]]; then
            echo "::notice::Specific environment '.tfvars' files changed."
            for file in $TFVARS_FILES; do
              # Construct the full relative path for the key to avoid collisions
              KEY_PATH="../../$file"
              # Check if this config already exists in our map
              if [[ -n "${CONFIGS_MAP[$KEY_PATH]}" ]]; then
                echo "Skipping $file as it was already included by a common.tfvars change."
                continue
              fi

              if [[ "$file" =~ ^01_clients/([^/]+)/([^/]+)/.*\.tfvars$ ]]; then
                CLIENT="${BASH_REMATCH[1]}"
                ENV="${BASH_REMATCH[2]}"
                COMMON_FILE="01_clients/$CLIENT/common.tfvars"
                # CORRECTED: workdir is now the wrapper module directory.
                WORKDIR="wrapper_modules/01_client"
                # CORRECTED: Paths are now relative from the wrapper module directory.
                CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"$CLIENT\",\"env\":\"$ENV\",\"backend_prefix\":\"terraform/$CLIENT/$ENV\",\"common_var_file\":\"../../$COMMON_FILE\",\"env_var_file\":\"../../$file\"}"
                add_config "$CONFIG"
              elif [[ "$file" =~ ^00_internal/.*\.tfvars$ ]]; then
                # CORRECTED: workdir is now the wrapper module directory.
                WORKDIR="wrapper_modules/00_internal"
                # CORRECTED: Path is now relative from the wrapper module directory.
                CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"internal\",\"env\":\"common\",\"backend_prefix\":\"terraform/saascada\",\"common_var_file\":\"\",\"env_var_file\":\"../../$file\"}"
                add_config "$CONFIG"
              fi
            done
          fi

          # --- Finalize and Output ---
          if [ ${#CONFIGS_MAP[@]} -eq 0 ]; then
            echo "No Terraform changes detected that require a plan."
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "matrix_json=[]" >> $GITHUB_OUTPUT
          else
            echo "Changes detected. Generating matrix for ${#CONFIGS_MAP[@]} environments."
            # Convert map values to a JSON array
            JSON_ARRAY=$(printf '%s\n' "${CONFIGS_MAP[@]}" | jq -c -s .)
            
            echo "Generated Matrix:"
            echo "$JSON_ARRAY" | jq # Pretty-print for logs

            echo "has_changes=true" >> $GITHUB_OUTPUT
            # Use heredoc syntax for robust multi-line output to GITHUB_OUTPUT
            {
              echo 'matrix_json<<EOF'
              echo "$JSON_ARRAY"
              echo 'EOF'
            } >> "$GITHUB_OUTPUT"
          fi

  terraform-operations:
    name: Terraform (${{ matrix.config.client }}/${{ matrix.config.env }})
    needs: discover-changes
    if: needs.discover-changes.outputs.has_changes == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    strategy:
      fail-fast: false
      matrix:
        config: ${{ fromJson(needs.discover-changes.outputs.matrix_json) }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.TERRAFORM_SERVICE_ACCOUNT }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Generate Backend Configuration
        run: |
          # The backend prefix is now correctly passed from the matrix.
          BACKEND_PREFIX="${{ matrix.config.backend_prefix }}"
          
          cat > backend.hcl << EOF
          bucket = "gcs-saascada-terraform-state-01"
          prefix = "$BACKEND_PREFIX"
          EOF
          
          echo "Generated backend.hcl for ${{ matrix.config.client }}/${{ matrix.config.env }}:"
          cat backend.hcl
        working-directory: ${{ matrix.config.workdir }}

      - name: Terraform Init
        id: init
        run: terraform init -backend-config=backend.hcl
        working-directory: ${{ matrix.config.workdir }}

      - name: Terraform Validate
        id: validate
        run: terraform validate
        working-directory: ${{ matrix.config.workdir }}

      - name: Build Terraform Plan Command
        id: build_plan_cmd
        shell: bash
        run: |
          VAR_FILES=""
          # Add common var file only if it's defined (for client modules).
          if [[ -n "${{ matrix.config.common_var_file }}" ]]; then
            VAR_FILES="-var-file=${{ matrix.config.common_var_file }}"
          fi
          # Add the main environment var file.
          VAR_FILES="$VAR_FILES -var-file=${{ matrix.config.env_var_file }}"

          CMD="terraform plan -input=false -out=${{ env.TF_PLAN_FILE }} -detailed-exitcode $VAR_FILES"
          echo "command=$CMD" >> $GITHUB_OUTPUT
          echo "Running command: $CMD"

      - name: Terraform Plan
        id: plan
        shell: bash
        run: |
          # Execute the plan command and capture its exit code.
          ${{ steps.build_plan_cmd.outputs.command }}
          PLAN_EXIT_CODE=$?

          # Set the exit code as a step output.
          echo "plan_exit_code=${PLAN_EXIT_CODE}" >> $GITHUB_OUTPUT

          # Provide clear feedback in the logs.
          echo "Terraform Plan exited with code: ${PLAN_EXIT_CODE}"
          if [[ ${PLAN_EXIT_CODE} -eq 1 ]]; then
            echo "::error::Terraform Plan failed. See the output above for details."
            exit 1
          elif [[ ${PLAN_EXIT_CODE} -eq 0 ]]; then
            echo "Plan successful. No changes detected."
            exit 0
          elif [[ ${PLAN_EXIT_CODE} -eq 2 ]]; then
            echo "Plan successful. Infrastructure changes are pending."
            exit 0
          fi
        working-directory: ${{ matrix.config.workdir }}
        
      - name: Terraform Apply
      
        if: |
          github.event_name == 'pull_request' &&
          github.event.action == 'closed' &&
          github.event.pull_request.merged == true &&
          github.event.pull_request.base.ref == 'main' &&
          (steps.plan.outputs.plan_exit_code == '0' || steps.plan.outputs.plan_exit_code == '2')
        run: |
          echo "Applying Terraform using plan file: ${{ env.TF_PLAN_FILE }}"
          terraform apply -auto-approve ${{ env.TF_PLAN_FILE }}
        working-directory: ${{ matrix.config.workdir }}
