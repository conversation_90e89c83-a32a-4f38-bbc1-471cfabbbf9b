variable "project_id" {
  description = "The GCP project ID."
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "client_prefix" {
  description = "Client prefix"
  type        = string
}

variable "service_accounts_config" {
  description = "A map of service accounts to create, keyed by a unique name."
  type = map(object({
    service_account_purpose = string # An identifier like "cloud-run", "storage", etc.
  }))
}
