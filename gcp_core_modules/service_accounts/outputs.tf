output "cloud_run_sa_member" {
  description = "The member identity of the service account for Cloud Run."
  value       = local.cloud_run_sa_key != null ? google_service_account.service_account[local.cloud_run_sa_key].member : null
}

# Outputs all created service account emails for reference
output "all_service_account_emails" {
  description = "The emails of all created service accounts."
  value       = { for key, sa in google_service_account.service_account : key => sa.email }
}
