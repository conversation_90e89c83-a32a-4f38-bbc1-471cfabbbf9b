locals {
  cloud_run_sa_key = try([for k, v in var.service_accounts_config : k if v.service_account_purpose == "cloud-run"][0], null)
}

resource "google_service_account" "service_account" {
  for_each     = var.service_accounts_config
  project      = var.project_id
  account_id   = "sa-${var.environment}-${var.client_prefix}-${each.key}"
  display_name = "Service Account for ${each.value.service_account_purpose}"
}

