
locals {
  all_subscriptions = flatten([
    for topic_key, topic_config in var.pubsub_config : [
      for sub_key, sub_config in topic_config.subscriptions : {
        subscription_name     = "${topic_key}-${sub_key}"
        topic_key             = topic_key
        config                = sub_config
      }
    ]
  ])
  subscriptions_map = { for sub in local.all_subscriptions : sub.subscription_name => sub }
}

############################ pub-sub topic #########################################

resource "google_pubsub_topic" "topic" {
  for_each                   = var.pubsub_config
  project                    = var.project_id
  name                       = each.key
  message_retention_duration = each.value.message_retention_duration
}

resource "google_pubsub_topic_iam_member" "publisher" {
  for_each = var.pubsub_iam_member != null ? var.pubsub_config : {}

  project = var.project_id
  topic   = google_pubsub_topic.topic[each.key].name
  role    = "roles/pubsub.publisher"
  member  = var.pubsub_iam_member
}

############################ pub-sub topic subscriptions #########################################

resource "google_pubsub_subscription" "subscription" {
  for_each                     = local.subscriptions_map
  project                      = var.project_id
  name                         = each.key
  topic                        = google_pubsub_topic.topic[each.value.topic_key].name
  labels                       = each.value.config.labels
  message_retention_duration   = each.value.config.message_retention_duration
  retain_acked_messages        = each.value.config.retain_acked_messages
  ack_deadline_seconds         = each.value.config.ack_deadline_seconds
  enable_message_ordering      = each.value.config.enable_message_ordering
  enable_exactly_once_delivery = each.value.config.enable_exactly_once_delivery

  expiration_policy {
    ttl = each.value.config.expiration_policy_ttl
  }
  dynamic "dead_letter_policy" {
    for_each = each.value.config.dead_letter_policy != null ? [each.value.config.dead_letter_policy] : []
    content {
      
      dead_letter_topic     = google_pubsub_topic.topic[dead_letter_policy.value.dead_letter_topic_key].id
      max_delivery_attempts = dead_letter_policy.value.max_delivery_attempts
    }
  }

  dynamic "retry_policy" {
    for_each = each.value.config.retry_policy != null ? [each.value.config.retry_policy] : []
    content {
      minimum_backoff = retry_policy.value.minimum_backoff
      maximum_backoff = retry_policy.value.maximum_backoff
    }
  }
}

resource "google_pubsub_subscription_iam_member" "subscriber" {
  for_each = var.pubsub_iam_member != null ? local.subscriptions_map : {}

  project      = var.project_id
  subscription = google_pubsub_subscription.subscription[each.key].name
  role         = "roles/pubsub.subscriber"
  member       = var.pubsub_iam_member
}
