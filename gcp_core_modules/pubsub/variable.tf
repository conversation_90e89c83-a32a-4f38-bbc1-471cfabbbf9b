
variable "project_id" {
  description = "The GCP project ID."
  type        = string
}

variable "pubsub_service_account" {
  description = "The single service account for all publisher and subscriber roles. If null, no IAM bindings are created."
  type        = string
  default     = null
}

variable "pubsub_iam_member" {
  description = "The IAM member identity for pubsub roles"
  type        = string
  default     = null
}

variable "pubsub_config" {
  description = "A map of Pub/Sub topics and their respective subscriptions and IAM configurations."
  type = map(object({
   
    message_retention_duration = optional(string)

    subscriptions = optional(map(object({
   
      ack_deadline_seconds         = optional(number)
      message_retention_duration   = optional(string)
      retain_acked_messages        = optional(bool, false)
      enable_message_ordering      = optional(bool, false)
      enable_exactly_once_delivery = optional(bool, false)
      labels                       = optional(map(string), {})
      expiration_policy_ttl        = optional(string, "")
      

      retry_policy = optional(object({
        minimum_backoff = optional(string)
        maximum_backoff = optional(string)
      }), null)

      dead_letter_policy = optional(object({
        dead_letter_topic_key = optional(string)
        max_delivery_attempts = optional(number, 5)
      }), null)
    })), {})
  }))
 
}
