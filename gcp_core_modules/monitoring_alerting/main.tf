##################################################
###### Log based Metrics & Alerting Policies #####
##################################################

resource "google_monitoring_monitored_project" "primary" {
  for_each = toset(var.monitored_project_ids)

  metrics_scope = var.alerting_project_id
  name          = each.value
}


module "alerting" {
  source = "./monitoring_module"

  for_each = { for idx, config in var.alert_config : idx => config }

  alerting_project_id         = var.alerting_project_id
  project_ids                 = var.project_ids
  create_firewall_metric      = each.value.create_firewall_metric
  create_network_metric       = each.value.create_network_metric
  create_route_metric         = each.value.create_route_metric
  create_iam_metric           = each.value.create_iam_metric
  create_custom_role_metric   = each.value.create_custom_role_metric
  create_owner_role_metric    = each.value.create_owner_role_metric
  create_bucket_iam_metric    = each.value.create_bucket_iam_metric
  create_gce_alert            = each.value.create_gce_alert
  create_sql_alert            = each.value.create_sql_alert
  create_pubsub_alert         = each.value.create_pubsub_alert
  create_gke_alert            = each.value.create_gke_alert
  create_redis_alert          = each.value.create_redis_alert
  create_cloud_run_alert      = each.value.create_cloud_run_alert
  create_cloud_function_alert = each.value.create_cloud_function_alert

  notification_channels = var.notification_channels

  depends_on = [ google_monitoring_monitored_project.primary ]
}
