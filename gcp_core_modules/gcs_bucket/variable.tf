variable "region" {
  type        = string
  default     = ""
  description = "region of the bucket"
}

variable "project_id" {
  type        = string
  default     = ""
  description = "Project ID where the bucket will be created"
}

variable "gcs_bucket_config" {
  type = map(object({
    bucket_name = string
  }))
  description = "Configuration for the bucket name."
}

variable "client_prefix" {
  type        = string
  default     = ""
  description = "client prefix for the bucket name"
}

variable "environment" {
  type        = string
  default     = ""
  description = "environment for the bucket name i.e. dev,stg,prod,qa etc."
}
