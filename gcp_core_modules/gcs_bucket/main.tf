resource "google_storage_bucket" "saascada-bucket" {
  for_each                    = var.gcs_bucket_config
  name                        = "gcs-${each.value.bucket_name}-${var.client_prefix}-${var.environment}"
  location                    = var.region
  force_destroy               = false
  project                     = var.project_id
  storage_class               = "STANDARD"
  public_access_prevention    = "enforced"
  uniform_bucket_level_access = true
  soft_delete_policy  {
    retention_duration_seconds = 604800
  }


}